#!/usr/bin/env python3
"""
验证 DeepEP 多节点通信能力的脚本
"""

import os
import sys
import torch
import torch.distributed as dist
import deep_ep
from utils import init_dist

def check_nvshmem_support():
    """检查 NVSHMEM 支持"""
    print("=== NVSHMEM 支持检查 ===")
    
    # 检查环境变量
    nvshmem_dir = os.getenv('NVSHMEM_DIR')
    print(f"NVSHMEM_DIR: {nvshmem_dir}")
    
    # 检查是否编译时启用了 NVSHMEM
    try:
        # 尝试创建一个需要 NVSHMEM 的 buffer
        group = dist.new_group([0])  # 单进程组用于测试
        buffer = deep_ep.Buffer(group, 0, int(1e6), low_latency_mode=False)  # 设置 RDMA 缓冲区
        print("✓ NVSHMEM 支持已启用")
        buffer.destroy()
        return True
    except Exception as e:
        print(f"✗ NVSHMEM 支持未启用: {e}")
        return False

def check_gpu_topology():
    """检查 GPU 拓扑"""
    print("\n=== GPU 拓扑检查 ===")
    
    if not torch.cuda.is_available():
        print("✗ CUDA 不可用")
        return False
    
    num_gpus = torch.cuda.device_count()
    print(f"可用 GPU 数量: {num_gpus}")
    
    if num_gpus < 2:
        print("✗ 需要至少 2 个 GPU 进行测试")
        return False
    
    # 检查 GPU 间连接
    print("GPU 设备信息:")
    for i in range(num_gpus):
        props = torch.cuda.get_device_properties(i)
        print(f"  GPU {i}: {props.name}, 内存: {props.total_memory // 1024**3} GB")
    
    return True

def test_intranode_communication(num_processes=2):
    """测试节点内通信"""
    print(f"\n=== 节点内通信测试 (进程数: {num_processes}) ===")
    
    def test_worker(local_rank, num_local_ranks):
        try:
            rank, num_ranks, group = init_dist(local_rank, num_local_ranks)
            print(f"进程 {rank}/{num_ranks} 启动成功")
            
            # 创建 buffer（只使用 NVL，不使用 RDMA）
            buffer = deep_ep.Buffer(group, int(1e9), 0, low_latency_mode=False, explicitly_destroy=True)
            print(f"进程 {rank}: Buffer 创建成功")
            
            # 简单的数据测试
            x = torch.ones((100, 512), dtype=torch.bfloat16, device='cuda') * rank
            print(f"进程 {rank}: 测试数据创建成功")
            
            # 清理
            buffer.destroy()
            dist.destroy_process_group()
            print(f"进程 {rank}: 测试完成")
            
        except Exception as e:
            print(f"进程 {local_rank} 错误: {e}")
            import traceback
            traceback.print_exc()
    
    try:
        torch.multiprocessing.spawn(test_worker, args=(num_processes,), nprocs=num_processes)
        print("✓ 节点内通信测试成功")
        return True
    except Exception as e:
        print(f"✗ 节点内通信测试失败: {e}")
        return False

def test_internode_capability():
    """测试节点间通信能力（不实际运行多节点）"""
    print("\n=== 节点间通信能力测试 ===")
    
    try:
        # 测试是否可以创建需要 RDMA 的配置
        group = dist.new_group([0])
        
        # 尝试创建 16 进程的配置（模拟 2 节点 x 8 GPU）
        print("测试 16 进程配置（模拟 2 节点）...")
        
        # 这里我们不实际创建 buffer，只检查配置
        config = deep_ep.Config(24, 8, 512, 16, 128)
        print("✓ 多节点配置创建成功")
        
        # 检查 buffer 大小提示
        nvl_size = config.get_nvl_buffer_size_hint(7168 * 2, 16)  # 16 ranks
        rdma_size = config.get_rdma_buffer_size_hint(7168 * 2, 16)
        print(f"推荐缓冲区大小 - NVL: {nvl_size // 1024**3} GB, RDMA: {rdma_size // 1024**3} GB")
        
        return True
        
    except Exception as e:
        print(f"✗ 节点间通信能力测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_low_latency_mode():
    """测试低延迟模式"""
    print("\n=== 低延迟模式测试 ===")
    
    def test_ll_worker(local_rank, num_local_ranks):
        try:
            rank, num_ranks, group = init_dist(local_rank, num_local_ranks)
            
            # 低延迟模式参数
            ll_num_tokens, ll_hidden, ll_num_experts = 16, 512, 64
            num_rdma_bytes = deep_ep.Buffer.get_low_latency_rdma_size_hint(
                ll_num_tokens, ll_hidden, num_ranks, ll_num_experts)
            
            print(f"进程 {rank}: 低延迟模式 RDMA 缓冲区大小: {num_rdma_bytes // 1024**2} MB")
            
            # 创建低延迟模式 buffer
            buffer = deep_ep.Buffer(
                group, int(1e9), num_rdma_bytes, 
                low_latency_mode=True,
                num_qps_per_rank=ll_num_experts // num_ranks,
                explicitly_destroy=True
            )
            print(f"进程 {rank}: 低延迟模式 Buffer 创建成功")
            
            buffer.destroy()
            dist.destroy_process_group()
            
        except Exception as e:
            print(f"进程 {local_rank} 低延迟模式错误: {e}")
            import traceback
            traceback.print_exc()
    
    try:
        torch.multiprocessing.spawn(test_ll_worker, args=(2,), nprocs=2)
        print("✓ 低延迟模式测试成功")
        return True
    except Exception as e:
        print(f"✗ 低延迟模式测试失败: {e}")
        return False

def main():
    print("DeepEP 多节点通信能力验证")
    print("=" * 50)
    
    # 初始化单进程分布式环境用于测试
    os.environ.setdefault('MASTER_ADDR', '127.0.0.1')
    os.environ.setdefault('MASTER_PORT', '12345')
    os.environ.setdefault('WORLD_SIZE', '1')
    os.environ.setdefault('RANK', '0')
    
    if not dist.is_initialized():
        dist.init_process_group(backend='nccl', world_size=1, rank=0)
    
    results = []
    
    # 1. 检查基础环境
    results.append(("GPU 拓扑", check_gpu_topology()))
    results.append(("NVSHMEM 支持", check_nvshmem_support()))
    
    # 2. 测试通信能力
    results.append(("节点内通信", test_intranode_communication(2)))
    results.append(("节点间配置", test_internode_capability()))
    results.append(("低延迟模式", test_low_latency_mode()))
    
    # 清理
    if dist.is_initialized():
        dist.destroy_process_group()
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结:")
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
    
    all_passed = all(result for _, result in results)
    print(f"\n总体结果: {'✓ 所有测试通过' if all_passed else '✗ 部分测试失败'}")
    
    if all_passed:
        print("\n建议:")
        print("1. 可以运行: python3 test_intranode.py")
        print("2. 可以运行: python3 test_internode.py --test-ll-compatibility")
        print("3. 如需真正的多节点测试，需要配置 RDMA 网络")
    
    return all_passed

if __name__ == "__main__":
    main()
